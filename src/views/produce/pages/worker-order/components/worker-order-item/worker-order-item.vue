<template>
  <a-drawer
    :open="props.open"
    :closable="false"
    :headerStyle="{ padding: '0 24px' }"
    :body-style="{ padding: 0 }"
    :width="drawerWidth"
    @close="handleClose"
  >
    <template #title>
      <div class="drawer-header">
        <div class="drawer-header-title">{{ drawerTitle }}</div>
        <div class="drawer-header-tabs">
          <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
            <a-tab-pane key="base" tab="基本信息" />
            <a-tab-pane key="custom" tab="自定义信息" />
            <a-tab-pane key="task" tab="生产任务" />
            <a-tab-pane key="bom" tab="用料清单" />
          </a-tabs>
        </div>
        <div class="drawer-header-btn">
          <a-tooltip>
            <template #title>{{ isMaximize ? '收起' : '全屏' }}</template>
            <a-button
              class="drawer-header-btn-item"
              type="text"
              :icon="h(isMaximize ? ShrinkOutlined : ArrowsAltOutlined)"
              @click="handleMaximize"
            />
          </a-tooltip>
          <a-divider type="vertical" />
          <a-button
            class="drawer-header-btn-item"
            type="text"
            :icon="h(CloseOutlined)"
            @click="handleClose"
          />
        </div>
      </div>
    </template>
    <!-- 内容区域 -->
    <div class="drawer-content" ref="scrollContainer">
      <div class="drawer-content-item" id="base-section">
        <div class="drawer-content-item__title">基本信息</div>
        <div class="drawer-content-item__content">
          <a-form layout="vertical" :model="form" :disabled="props.type === 'view'">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="工单编号">
                  <a-input v-model:value="form.orderNo" placeholder="请输入,忽略将自动生成" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="计划时间" required>
                  <template #tooltip>
                    <div class="plan-time" :class="{ 'plan-time-expired': isExpired }">
                      {{ getRemainingTime(form.planTime[1]) }}
                    </div>
                  </template>
                  <a-range-picker
                    style="width: 100%"
                    v-model:value="form.planTime"
                    :show-time="{ format: 'HH:mm' }"
                    format="YYYY-MM-DD HH:mm"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="状态">
                  <div class="status-box">
                    <CustomTag color="#000" text="未开始" />
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="产品信息" required>
                  <template #tooltip>
                    <div class="product-info">
                      <span class="product-info-btn" @click="showAddProductModal = true">
                        <PlusOutlined style="margin-right: 4px" /> 创建
                      </span>
                      <a-divider type="vertical" />
                      <span class="product-info-btn" @click="showAdvancedSelectionModal = true">
                        <SearchOutlined style="margin-right: 4px" /> 高级选择
                      </span>
                    </div>
                  </template>
                  <a-select v-model:value="form.productId" placeholder="请选择状态" />
                  <template #extra>
                    <div class="product-detail detail-box">
                      <div class="info-block product-detail__left">
                        <div class="product-row">
                          <span class="product-row__label">产品编号</span>
                          <span class="product-row__value">CP20230004</span>
                        </div>
                        <div class="product-row">
                          <span class="product-row__label">产品名称</span>
                          <span class="product-row__value">G031</span>
                        </div>
                        <div class="product-row">
                          <span class="product-row__label">产品规格</span>
                          <span class="product-row__value">1250T</span>
                        </div>
                      </div>
                      <div class="info-block product-detail__right">
                        <div class="stock-amount">{{ formatNumber(1000) }}</div>
                        <div class="stock-unit-text">库存数量(<span>只</span>)</div>
                      </div>
                    </div>
                  </template>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="计划数" required>
                  <a-input-number style="width: 100%" v-model:value="form.planCount" />
                  <template #extra>
                    <div class="detail-box plan-count-extra">
                      <div class="info-block plan-count__item">
                        <div class="amount good">{{ formatNumber(1000) }}</div>
                        <div class="label">良品数</div>
                      </div>
                      <div class="info-block plan-count__item">
                        <div class="amount bad">{{ formatNumber(1000) }}</div>
                        <div class="label">不良品数</div>
                      </div>
                      <div class="info-block plan-count__item">
                        <div class="amount rate">{{ formatNumber(1000) }}</div>
                        <div class="label">不良品率</div>
                      </div>
                    </div>
                  </template>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="备注">
                  <a-textarea
                    v-model:value="form.remark"
                    :autoSize="{ minRows: 5, maxRows: 5 }"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="关联单据">
                  <a-input v-model:value="form.relatedOrder" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>
      <div class="drawer-content-item" id="custom-section">
        <div class="drawer-content-item__title">自定义信息</div>
        <div class="drawer-content-item__content"></div>
      </div>
      <div class="drawer-content-item" id="task-section">
        <div class="drawer-content-item__title">生产任务</div>
        <div class="drawer-content-item__content">
          <ConfigurableTable
            :draggable="true"
            :columns="taskColumns"
            :data-source="taskData"
            :pagination="false"
          >
            <template #toolbar-left>
              <a-space>
                <a-button type="primary" :icon="h(PlusCircleFilled)">添加任务</a-button>
                <a-button type="default" :icon="h(SaveOutlined)">保存工艺路线</a-button>
              </a-space>
            </template>
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'action'">
                <a-space>
                  <a>编辑</a>
                  <a>复制</a>
                  <a>删除</a>
                </a-space>
              </template>
            </template>
          </ConfigurableTable>
        </div>
      </div>
      <div class="drawer-content-item" id="bom-section">
        <div class="drawer-content-item__title">用料清单</div>
        <div class="drawer-content-item__content">
          <ConfigurableTable
            :draggable="true"
            :columns="materialColumns"
            :data-source="materialData"
            :pagination="false"
          >
            <template #toolbar-left>
              <a-space>
                <a-button type="primary" :icon="h(PlusCircleFilled)">添加用料</a-button>
                <a-button type="default" :icon="h(FileTextOutlined)">按BOM添加</a-button>
                <a-button type="default" :icon="h(SaveOutlined)">保存物料清单</a-button>
                <a-button type="default" :icon="h(ReloadOutlined)">更新物料清单</a-button>
              </a-space>
            </template>
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'action'">
                <a-space>
                  <a>编辑</a>
                  <a>复制</a>
                  <a>删除</a>
                </a-space>
              </template>
            </template>
          </ConfigurableTable>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <a-button class="drawer-footer__btn" type="default" @click="handleClose">取消</a-button>
        <a-button class="drawer-footer__btn" type="primary" @click="handleCreate">创建</a-button>
      </div>
    </template>
  </a-drawer>
  <AddProductModal
    :open="showAddProductModal"
    @cancel="showAddProductModal = false"
    @confirm="handleConfirm"
  />
  <AdvancedSelectionModal
    :open="showAdvancedSelectionModal"
    @cancel="showAdvancedSelectionModal = false"
    @confirm="handleSelectProduct($event)"
  />
</template>

<script setup lang="ts">
import { ref, computed, h, onMounted, onUnmounted, nextTick } from 'vue'
import {
  PlusCircleFilled,
  SaveOutlined,
  CloseOutlined,
  ArrowsAltOutlined,
  ShrinkOutlined,
  PlusOutlined,
  SearchOutlined,
  FileTextOutlined,
  ReloadOutlined,
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

import { formatNumber, getRemainingTime } from '@/utils'
import { taskColumns, materialColumns } from './columns'
import { mockData, materialData as materialMockData } from './mockData'

import CustomTag from '@/components/CustomTag/index.vue'
import ConfigurableTable from '@/components/ConfigurableTable/index.vue'
import AddProductModal from '../add-product-modal/add-product-modal.vue'
import AdvancedSelectionModal from '../advanced-selection-modal/advanced-selection-modal.vue'

const props = withDefaults(
  defineProps<{
    open: boolean
    orderId: string | number
    type: 'create' | 'edit' | 'view'
  }>(),
  {
    type: 'create',
  },
)
const emit = defineEmits(['update:open', 'close'])

const drawerTitle = computed(() => {
  return props.type === 'create' ? '创建工单' : props.type === 'edit' ? '编辑工单' : '工单详情'
})
const drawerWidth = ref<string>('1536px')
const isMaximize = ref<boolean>(false)
const activeTab = ref('base')
const scrollContainer = ref<HTMLElement>()
const isScrollingToAnchor = ref(false)

const showAddProductModal = ref(false)
const showAdvancedSelectionModal = ref(false)

const form = ref({
  orderNo: '',
  planTime: [dayjs().startOf('day'), dayjs().endOf('day')],
  status: '',
  productId: '',
  planCount: undefined,
  remark: '',
  relatedOrder: '',
})
const taskData = ref(mockData)
const materialData = ref(materialMockData)

const isExpired = computed(() => {
  return form.value.planTime[1].isBefore(dayjs())
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    if (scrollContainer.value) {
      scrollContainer.value.addEventListener('scroll', handleScroll)
    }
  })
})

onUnmounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', handleScroll)
  }
})

/**
 *
 * 点击tab时滚动到对应位置，或滚动页面时更新tab
 */
// 锚点映射
const anchorMap = {
  base: 'base-section',
  custom: 'custom-section',
  task: 'task-section',
  bom: 'bom-section',
}

// 点击tab时滚动到对应位置
const handleTabChange = (key: string) => {
  const targetId = anchorMap[key as keyof typeof anchorMap]
  const targetElement = document.getElementById(targetId)

  if (targetElement && scrollContainer.value) {
    isScrollingToAnchor.value = true

    // 计算目标位置，使用offsetTop获取相对位置
    const scrollTop = targetElement.offsetTop - 24 // 24px为padding

    // 平滑滚动
    scrollContainer.value.scrollTo({
      top: scrollTop,
      behavior: 'smooth',
    })

    // 滚动完成后重置标识
    setTimeout(() => {
      isScrollingToAnchor.value = false
    }, 500)
  }
}

// 监听滚动，更新当前激活的tab
const handleScroll = () => {
  if (isScrollingToAnchor.value || !scrollContainer.value) return

  const container = scrollContainer.value
  const scrollTop = container.scrollTop

  // 获取各个section的位置信息
  const sections = Object.entries(anchorMap)
    .map(([key, id]) => {
      const element = document.getElementById(id)
      if (element) {
        // 计算元素相对于滚动容器的偏移量
        const offsetTop = element.offsetTop
        return { key, offsetTop, element }
      }
      return null
    })
    .filter(Boolean) as Array<{ key: string; offsetTop: number; element: HTMLElement }>

  // 按照offsetTop排序
  sections.sort((a, b) => a.offsetTop - b.offsetTop)

  // 找到当前应该激活的section
  let currentSection = sections[0]
  for (const section of sections) {
    // 如果滚动位置超过了section的位置（加上一个偏移量）
    if (scrollTop + 150 >= section.offsetTop) {
      currentSection = section
    } else {
      break
    }
  }

  // 更新激活的tab
  if (currentSection && activeTab.value !== currentSection.key) {
    activeTab.value = currentSection.key
  }
}

/**
 * 顶部按钮操作
 */
// 最大化
const handleMaximize = () => {
  isMaximize.value = !isMaximize.value
  if (isMaximize.value) {
    drawerWidth.value = '100%'
  } else {
    drawerWidth.value = '1536px'
  }
}
// 关闭
const handleClose = () => {
  emit('close')
}

const handleConfirm = (formData: any) => {
  showAddProductModal.value = false
  console.log('confirm', formData)
}

const handleSelectProduct = (e: string[]) => {
  console.log('select product', e)
}

/**
 * 底部按钮操作
 */
// 创建工单
const handleCreate = () => {
  const { planTime, ...rest } = form.value
  const params = {
    ...rest,
    planTime: planTime.map((item) => item.format('YYYY-MM-DD HH:mm')),
    taskList: taskData.value,
    materialList: materialData.value,
  }
  console.log(params)
}
</script>

<style src="./index.scss" scoped></style>
